import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:kairos/core/theme/color_schemes.dart';
import 'package:kairos/features/finances/domain/entities/finances_unpaid_response_entity.dart';

enum PaymentMethod { wave, orangeMoney }

class PaymentDialog extends StatefulWidget {
  final FinancesUnpaidResponseEntity unpaidFeesResponse;
  final VoidCallback onPaymentConfirmed;

  const PaymentDialog({
    super.key,
    required this.unpaidFeesResponse,
    required this.onPaymentConfirmed,
  });

  @override
  State<PaymentDialog> createState() => _PaymentDialogState();
}

class _PaymentDialogState extends State<PaymentDialog> {
  PaymentMethod? _selectedPaymentMethod;
  final TextEditingController _amountController = TextEditingController();
  double _enteredAmount = 0.0;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _amountController.addListener(_onAmountChanged);
  }

  @override
  void dispose() {
    _amountController.removeListener(_onAmountChanged);
    _amountController.dispose();
    super.dispose();
  }

  void _onAmountChanged() {
    final text = _amountController.text.replaceAll(' ', '').replaceAll(',', '.');
    final amount = double.tryParse(text) ?? null;
    
    setState(() {
      _enteredAmount = amount ?? 0.0;
      _errorMessage = '';
      
      // Validate amount
      final totalUnpaid = widget.unpaidFeesResponse.totalImpaye ?? 0.0;
      if (amount != null && amount > totalUnpaid) {
        _errorMessage = 'Le montant ne peut pas dépasser le total impayé';
      } else if (amount != null && amount <= 0) {
        _errorMessage = 'Veuillez saisir un montant valide';
      }
    });
  }

  String _formatAmount(double amount) {
    return NumberFormat.decimalPattern('fr_FR').format(amount);
  }

  void _onValidatePressed() {
    if (_selectedPaymentMethod == null) {
      setState(() {
        _errorMessage = 'Veuillez sélectionner un moyen de paiement';
      });
      return;
    }

    if (_enteredAmount <= 0) {
      setState(() {
        _errorMessage = 'Veuillez saisir un montant valide';
      });
      return;
    }

    final totalUnpaid = widget.unpaidFeesResponse.totalImpaye ?? 0.0;
    if (_enteredAmount > totalUnpaid) {
      setState(() {
        _errorMessage = 'Le montant ne peut pas dépasser le total impayé';
      });
      return;
    }
  }


  @override
  Widget build(BuildContext context) {
    final totalUnpaid = widget.unpaidFeesResponse.totalImpaye ?? 0.0;
    final remainingBalance = totalUnpaid - _enteredAmount;

    return Stack(
      children: [
        Dialog(
          backgroundColor: Colors.white,
          insetPadding: const EdgeInsets.all(10),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            width: 377,
            height: 514,
            padding: const EdgeInsets.all(22),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.5),
                  offset: const Offset(0, 6),
                  blurRadius: 6,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                const Text(
                  'Sélectionnez un moyen de paiement',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: AppColorSchemes.primaryBlue,
                  ),
                ),
                const SizedBox(height: 16),

                // Payment method selection
                _buildPaymentMethodSelection(),
                const SizedBox(height: 24),
                // Amount input field
                _buildAmountInputField(),
                const SizedBox(height: 24),
                // Dynamic amount display
                _buildAmountDisplay(totalUnpaid, remainingBalance),
                const Spacer(),
                // Action buttons
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentMethodSelection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Wave payment method
        GestureDetector(
          onTap: () {
            setState(() {
              _selectedPaymentMethod = PaymentMethod.wave;
            });
          },
          child: Container(
            width: 130,
            height: 83,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              border: Border.all(
                color: _selectedPaymentMethod == PaymentMethod.wave
                    ? const Color(0xFFF77000)
                    : Colors.grey.withValues(alpha: 0.8),
                width: _selectedPaymentMethod == PaymentMethod.wave ? 4 : 3,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(2.0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(5),
                child: Image.asset(
                  'assets/images/wave-payment.png',
                  fit: BoxFit.fill,
                ),
              ),
            ),
          ),
        ),

        // Orange Money payment method
        GestureDetector(
          onTap: () {
            setState(() {
              _selectedPaymentMethod = PaymentMethod.orangeMoney;
            });
          },
          child: Container(
            width: 130,
            height: 83,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              border: Border.all(
                color: _selectedPaymentMethod == PaymentMethod.orangeMoney
                    ? const Color(0xFFF77000)
                    : Colors.grey.withValues(alpha: 0.8),
                width: _selectedPaymentMethod == PaymentMethod.orangeMoney ? 4 : 3,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(2.0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(5),
                child: Image.asset(
                  'assets/images/orange-money-payment.jpeg',
                  fit: BoxFit.fill,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAmountInputField() {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Padding(
            padding: EdgeInsets.only(left: 0, top: 8),
            child: Text(
              'Saisissez le montant à payer',
                  textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: AppColorSchemes.primaryBlue,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _amountController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'[0-9,.]')),
                    ],
                    decoration: const InputDecoration(
                      hintText: 'Ex : 15 000',
                      suffixIcon: Padding(
                        padding: EdgeInsets.only(top: 16.0),
                        child: Text(
                          'FCFA',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.w900,
                            fontSize: 12,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      hintStyle: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                        color: Colors.black,
                      ),
                      border: InputBorder.none,
                    ),
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                      color: Colors.black,
                    ),
                  ),
                ),
                
              ],
            ),
          ),
          //  Error message
                if (_errorMessage.isNotEmpty)
                  Padding(
                  padding: EdgeInsets.only(left: 0, top: 0),
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 10,
                      ),
                    ),
                  ),
        ],
      );
  }

  Widget _buildAmountDisplay(double totalUnpaid, double remainingBalance) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(children: [
          TextSpan(text: ('Montant à payer : '), style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
            color: AppColorSchemes.primaryBlue,
          )) ,
          TextSpan(text: _formatAmount(_enteredAmount), style: TextStyle(
            fontWeight: FontWeight.w900,
            fontSize: 14,
          )), 
          TextSpan(text: ' FCFA', style: TextStyle(
            fontWeight: FontWeight.w900,
            fontSize: 8,
          )) 
        ]),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 12,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Text.rich(
          TextSpan(children: [
          TextSpan(text: ('Reliquat : '), style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
            color: AppColorSchemes.primaryBlue,
          )) ,
          TextSpan(text: _formatAmount(remainingBalance), style: TextStyle(
            fontWeight: FontWeight.w900,
            fontSize: 14,
          )), 
          TextSpan(text: ' FCFA', style: TextStyle(
            fontWeight: FontWeight.w900,
            fontSize: 8,
          )) 
        ]),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 16,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        // QUITTER button
        Expanded(
          child: Container(
            height: 50,
            decoration: BoxDecoration(
              color: const Color(0xFF920000),
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF16196E).withValues(alpha: 0.08),
                  offset: const Offset(0, 6),
                  blurRadius: 16,
                ),
              ],
            ),
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'ANNULER',
                style: TextStyle(
                 
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // VALIDER button
        Expanded(
          child: Container(
            height: 50,
            decoration: BoxDecoration(
              color: const Color(0xFF06B6E4),
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF16196E).withValues(alpha: 0.08),
                  offset: const Offset(0, 6),
                  blurRadius: 16,
                ),
              ],
            ),
            child: TextButton(
              onPressed: _onValidatePressed,
              child: const Text(
                'VALIDER',
                style: TextStyle(
                 
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

}
