import 'package:flutter/material.dart';
import 'package:kairos/core/theme/color_schemes.dart';

/// A reusable custom AlertDialog component.
///
/// This dialog displays a title, content, a cancel button, and a primary action button.
/// The primary action button is styled with AppColorSchemes.errorRed and executes
/// the provided [primaryActionCallback].
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/features/profile/presentation/bloc/profile_cubit.dart';
import 'package:kairos/features/profile/presentation/bloc/profile_state.dart';

/// A reusable custom AlertDialog component.
///
/// This dialog displays a title, content, a cancel button, and a primary action button.
/// The primary action button is styled with AppColorSchemes.errorRed and executes
/// the provided [primaryActionCallback].
class AppAlertDialog extends StatefulWidget {
  /// The title of the dialog.
  final String title;

  /// The content widget to display in the dialog body.
  final Widget content;

  /// The callback function to execute when the primary action button is pressed.
  final VoidCallback primaryActionCallback;

  /// The text to display on the primary action button. Defaults to 'CONFIRMER'.
  final String primaryActionText;

  /// The text to display on the cancel button. Defaults to 'ANNULER'.
  final String cancelActionText;

  /// Whether the dialog can be dismissed by tapping outside or pressing the back button.
  /// Defaults to true. Set to false to make it non-dismissible during sensitive operations.
  final bool barrierDismissible;

  /// Creates an [AppAlertDialog].
  const AppAlertDialog({
    super.key,
    required this.title,
    required this.content,
    required this.primaryActionCallback,
    this.primaryActionText = 'CONFIRMER',
    this.cancelActionText = 'ANNULER',
    this.barrierDismissible = true, // Default to true for general use
  });

  @override
  State<AppAlertDialog> createState() => _AppAlertDialogState();
}

class _AppAlertDialogState extends State<AppAlertDialog> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: widget.barrierDismissible, // Control back button dismissal
      child: BlocListener<ProfileCubit, ProfileState>(
        listener: (context, state) {
          // Dismiss the dialog programmatically when ProfileDeleted state is reached
          if (state is ProfileDeleted) {
            Navigator.of(context).pop();
          }
        },
        child: AlertDialog(
          backgroundColor: Colors.white,
          icon: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Material(
                color: Theme.of(context).colorScheme.secondary, // Use secondary color as background
                shape: const CircleBorder(),
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () {
                     // Only dismiss if allowed
                      Navigator.of(context).pop();
                    
                  },
                  child: const Padding(
                    padding: EdgeInsets.all(4.0),
                    child: Icon(Icons.close, size: 24, color: Colors.white), // White icon
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: Align(
                      alignment: Alignment.center,
                      child: Image.asset(
                        "assets/icons/information-alert.png",
                        width: 35,
                        height: 35,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          title: Text(
            widget.title,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          insetPadding: const EdgeInsets.symmetric(horizontal: 10),
          content: widget.content,
          actionsAlignment: MainAxisAlignment.spaceEvenly, // Align buttons to start and end
          actionsOverflowAlignment: OverflowBarAlignment.center,
          actionsPadding: const EdgeInsets.all(15.0),
          actionsOverflowButtonSpacing: 10,
          actions: [
            // Primary action button (Left-aligned)
            BlocBuilder<ProfileCubit, ProfileState>(
              builder: (context, state) {
                final bool isLoading = state is ProfileDeleting || state is LogoutLoading;
                return FilledButton(
                  style: ButtonStyle(
                    foregroundColor: WidgetStateProperty.all(Colors.white),
                    minimumSize: WidgetStateProperty.all(const Size(150, 50)),
                    maximumSize: WidgetStateProperty.all(const Size(150, 50)),
                    backgroundColor: WidgetStateProperty.all(AppColorSchemes.errorRed),
                  ),
                  onPressed: isLoading ? null : () {
                    // Do not dismiss dialog here, it will be dismissed by BlocListener on ProfileDeleted
                    widget.primaryActionCallback();
                  },
                  child: isLoading
                      ? const Center(
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(widget.primaryActionText),
                );
              },
            ),
            // Cancel button (Right-aligned)
            FilledButton(
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                    minimumSize: WidgetStateProperty.all(const Size(150, 50)), // Ensure consistent size
                  ),
                  onPressed: () {
                    Navigator.of(context).pop(); // Dismiss the dialog
                  },
                  child: Text(widget.cancelActionText),
                ),
          ],
        ),
      ),
    );
  }
}